#!/bin/bash
# Quick test script for AI Terminal installation

echo "🧪 Testing AI Terminal Installation..."
echo ""

# Test global commands
echo "Testing global commands:"
commands=("ait" "ai-chat" "ai-quick" "ai-health" "ai-config" "ai-setup")

for cmd in "${commands[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "✅ $cmd - Found"
    else
        echo "❌ $cmd - Not found"
    fi
done

echo ""

# Test project directory
if [[ -d "$HOME/ai-terminal" ]]; then
    echo "✅ Project directory exists: $HOME/ai-terminal"
    
    cd "$HOME/ai-terminal"
    
    # Test CLI
    if [[ -f "cli.py" ]]; then
        echo "✅ CLI script found"
        
        # Test help command
        if python3 cli.py --help >/dev/null 2>&1; then
            echo "✅ CLI is functional"
        else
            echo "❌ CLI has issues"
        fi
    else
        echo "❌ CLI script not found"
    fi
    
    # Test dependencies
    if [[ -f "pyproject.toml" ]] && command -v poetry >/dev/null 2>&1; then
        echo "✅ Poetry project detected"
        if poetry check >/dev/null 2>&1; then
            echo "✅ Dependencies are satisfied"
        else
            echo "⚠️  Dependencies may need updating"
        fi
    elif [[ -d "venv" ]]; then
        echo "✅ Virtual environment found"
    else
        echo "⚠️  No virtual environment detected"
    fi
else
    echo "❌ Project directory not found"
fi

echo ""

# Test configuration
config_locations=(
    "$HOME/.config/ai-terminal/config.yaml"
    "$HOME/.ai-terminal/config.yaml"
    "$HOME/ai-terminal/.ai-terminal/config.yaml"
)

config_found=false
for config in "${config_locations[@]}"; do
    if [[ -f "$config" ]]; then
        echo "✅ Configuration found: $config"
        config_found=true
        break
    fi
done

if [[ "$config_found" == "false" ]]; then
    echo "⚠️  No configuration found - run 'ai-setup' to configure"
fi

echo ""
echo "🎯 Installation test complete!"
echo ""
echo "If all tests passed, try:"
echo "  ait          # Activate environment"
echo "  ai-health    # Check system health"
echo "  ai-setup     # Configure AI providers"
