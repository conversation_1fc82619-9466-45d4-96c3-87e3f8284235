#!/bin/bash
# =============================================================================
# AI Terminal - Fully Automatic WSL Installation Script
# =============================================================================
# This script automatically installs and configures AI Terminal in WSL
# Compatible with: Ubuntu, Debian, and other Debian-based WSL distributions
# 
# Usage: 
#   1. Copy this script to your WSL environment
#   2. Make it executable: chmod +x auto_install_wsl.sh
#   3. Run it: ./auto_install_wsl.sh
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-terminal"
INSTALL_DIR="$HOME/$PROJECT_NAME"
WINDOWS_PROJECT_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
PYTHON_VERSION="3.11"

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🚀 AI Terminal WSL Auto-Installer                       ║"
    echo "║                                                                              ║"
    echo "║  This script will automatically install and configure AI Terminal in WSL    ║"
    echo "║  • Install Python 3.11+ and Poetry                                         ║"
    echo "║  • Copy project files from Windows                                          ║"
    echo "║  • Install all dependencies                                                 ║"
    echo "║  • Run interactive setup wizard                                             ║"
    echo "║  • Create convenient shortcuts                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}[STEP] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# Installation Functions
# =============================================================================

check_wsl() {
    print_step "Checking WSL environment..."
    
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft\|WSL" /proc/version; then
        print_error "This script must be run in WSL (Windows Subsystem for Linux)"
        exit 1
    fi
    
    print_success "WSL environment detected"
}

update_system() {
    print_step "Updating system packages..."
    
    sudo apt update -qq > /dev/null 2>&1
    sudo apt upgrade -y -qq > /dev/null 2>&1
    
    print_success "System packages updated"
}

install_dependencies() {
    print_step "Installing system dependencies..."
    
    sudo apt install -y -qq \
        software-properties-common \
        build-essential \
        curl \
        git \
        wget \
        unzip \
        tree \
        htop \
        jq \
        libssl-dev \
        libffi-dev \
        python3-dev \
        pkg-config \
        ca-certificates \
        gnupg \
        lsb-release > /dev/null 2>&1
    
    print_success "System dependencies installed"
}

install_python() {
    print_step "Installing Python $PYTHON_VERSION..."
    
    if check_command "python$PYTHON_VERSION"; then
        print_success "Python $PYTHON_VERSION already installed"
        return
    fi
    
    # Add deadsnakes PPA for latest Python versions
    sudo add-apt-repository -y ppa:deadsnakes/ppa > /dev/null 2>&1
    sudo apt update -qq > /dev/null 2>&1
    
    # Install Python and related packages
    sudo apt install -y -qq \
        python$PYTHON_VERSION \
        python$PYTHON_VERSION-venv \
        python$PYTHON_VERSION-pip \
        python$PYTHON_VERSION-dev \
        python$PYTHON_VERSION-distutils > /dev/null 2>&1
    
    # Create symlinks for convenience
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python3 2>/dev/null || true
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python 2>/dev/null || true
    
    print_success "Python $PYTHON_VERSION installed"
}

install_poetry() {
    print_step "Installing Poetry package manager..."
    
    if check_command "poetry"; then
        print_success "Poetry already installed"
        return
    fi
    
    # Install Poetry
    curl -sSL https://install.python-poetry.org | python3 - > /dev/null 2>&1
    
    # Add Poetry to PATH
    export PATH="$HOME/.local/bin:$PATH"
    
    # Add to shell profile
    if ! grep -q "/.local/bin" ~/.bashrc; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi
    
    print_success "Poetry installed"
}

copy_project() {
    print_step "Copying AI Terminal project from Windows..."
    
    # Check if Windows project exists
    if [[ ! -d "$WINDOWS_PROJECT_PATH" ]]; then
        print_error "Windows project not found at: $WINDOWS_PROJECT_PATH"
        print_info "Please ensure the project exists in Windows or update the path in this script"
        exit 1
    fi
    
    # Remove existing installation if it exists
    if [[ -d "$INSTALL_DIR" ]]; then
        print_warning "Removing existing installation..."
        rm -rf "$INSTALL_DIR"
    fi
    
    # Copy project files
    cp -r "$WINDOWS_PROJECT_PATH" "$INSTALL_DIR"
    
    # Set proper permissions
    chmod -R 755 "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/cli.py" 2>/dev/null || true
    
    print_success "Project copied to $INSTALL_DIR"
}

install_project_dependencies() {
    print_step "Installing project dependencies..."
    
    cd "$INSTALL_DIR"
    
    # Check if pyproject.toml exists (Poetry project)
    if [[ -f "pyproject.toml" ]]; then
        print_info "Using Poetry for dependency management..."
        poetry install > /dev/null 2>&1
        print_success "Dependencies installed with Poetry"
    elif [[ -f "requirements.txt" ]]; then
        print_info "Using pip for dependency management..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip > /dev/null 2>&1
        pip install -r requirements.txt > /dev/null 2>&1
        print_success "Dependencies installed with pip"
    else
        print_warning "No dependency file found, installing common packages..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip > /dev/null 2>&1
        pip install rich typer pydantic aiohttp > /dev/null 2>&1
        print_success "Basic dependencies installed"
    fi
}

create_shortcuts() {
    print_step "Creating convenient shortcuts..."

    # Create activation script
    cat > "$INSTALL_DIR/activate.sh" << 'EOF'
#!/bin/bash
# AI Terminal Environment Activation Script

cd ~/ai-terminal

if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    echo "🚀 Activating Poetry environment..."
    poetry shell
elif [[ -d "venv" ]]; then
    echo "🐍 Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️  No virtual environment found"
fi

echo "✅ AI Terminal environment ready!"
echo ""
echo "🎯 Available commands:"
echo "  python cli.py chat          - Start interactive chat"
echo "  python cli.py quick 'msg'   - Send quick message"
echo "  python cli.py health        - Check system health"
echo "  python cli.py config --show - Show configuration"
echo "  python cli.py setup         - Run setup wizard"
echo ""

exec bash
EOF

    chmod +x "$INSTALL_DIR/activate.sh"

    # Add aliases to bashrc
    if ! grep -q "ai-terminal aliases" ~/.bashrc; then
        cat >> ~/.bashrc << EOF

# ai-terminal aliases
alias ait='cd ~/ai-terminal && bash activate.sh'
alias ai-chat='cd ~/ai-terminal && python cli.py chat'
alias ai-quick='cd ~/ai-terminal && python cli.py quick'
alias ai-health='cd ~/ai-terminal && python cli.py health'
alias ai-config='cd ~/ai-terminal && python cli.py config --show'
alias ai-setup='cd ~/ai-terminal && python cli.py setup'
EOF
    fi

    print_success "Shortcuts created"
}

test_installation() {
    print_step "Testing installation..."

    cd "$INSTALL_DIR"

    # Test basic CLI functionality
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        if poetry run python cli.py --help &> /dev/null; then
            print_success "CLI test passed with Poetry"
        else
            print_error "CLI test failed with Poetry"
            return 1
        fi
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        if python cli.py --help &> /dev/null; then
            print_success "CLI test passed with venv"
        else
            print_error "CLI test failed with venv"
            return 1
        fi
    else
        if python3 cli.py --help &> /dev/null; then
            print_success "CLI test passed with system Python"
        else
            print_error "CLI test failed"
            return 1
        fi
    fi
}

run_setup_wizard() {
    print_step "Launching AI Terminal setup wizard..."

    cd "$INSTALL_DIR"

    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                          🎯 Setup Wizard Starting                           ║"
    echo "║                                                                              ║"
    echo "║  The interactive setup wizard will now guide you through:                   ║"
    echo "║  • Selecting your AI provider (OpenAI, Deepseek, Anthropic, Ollama)        ║"
    echo "║  • Configuring API keys (with multiple input methods)                       ║"
    echo "║  • Setting preferences (temperature, tokens, security, theme)               ║"
    echo "║                                                                              ║"
    echo "║  💡 Tip: Have your API key ready for easy pasting!                         ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo "Press Enter to start the setup wizard..."
    read

    # Run setup wizard
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        poetry run python cli.py setup
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        python cli.py setup
    else
        python3 cli.py setup
    fi
}

show_completion() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🎉 Installation Complete! 🎉                           ║"
    echo "║                                                                              ║"
    echo "║  AI Terminal has been successfully installed and configured in WSL!         ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}📍 Installation Location:${NC} $INSTALL_DIR"
    echo ""
    echo -e "${YELLOW}🚀 Quick Start Commands:${NC}"
    echo -e "  ${BLUE}ait${NC}                    - Activate AI Terminal environment"
    echo -e "  ${BLUE}ai-chat${NC}                - Start interactive chat"
    echo -e "  ${BLUE}ai-quick 'message'${NC}     - Send quick message"
    echo -e "  ${BLUE}ai-health${NC}              - Check system health"
    echo -e "  ${BLUE}ai-config${NC}              - Show configuration"
    echo -e "  ${BLUE}ai-setup${NC}               - Re-run setup wizard"
    echo ""
    echo -e "${YELLOW}📚 Manual Commands:${NC}"
    echo -e "  ${BLUE}cd ~/ai-terminal${NC}       - Navigate to project"
    echo -e "  ${BLUE}poetry shell${NC}           - Activate Poetry environment"
    echo -e "  ${BLUE}python cli.py chat${NC}     - Start chat session"
    echo ""
    echo -e "${GREEN}✨ Enjoy using AI Terminal! ✨${NC}"
    echo ""
    echo -e "${PURPLE}💡 Tip: Restart your terminal or run 'source ~/.bashrc' to use the new aliases${NC}"
}

# =============================================================================
# Main Installation Process
# =============================================================================

main() {
    print_header

    echo "Starting automatic installation in 3 seconds..."
    sleep 3

    # Core installation steps
    check_wsl
    update_system
    install_dependencies
    install_python
    install_poetry
    copy_project
    install_project_dependencies
    create_shortcuts
    test_installation

    # Interactive setup
    run_setup_wizard

    # Completion
    show_completion

    # Source bashrc to enable aliases
    echo "Reloading shell configuration..."
    source ~/.bashrc 2>/dev/null || true

    echo ""
    echo -e "${GREEN}🎯 Ready to use! Try: ${BLUE}ait${NC}"
}

# =============================================================================
# Error Handling
# =============================================================================

trap 'print_error "Installation failed at line $LINENO. Check the error above."' ERR

# =============================================================================
# Script Execution
# =============================================================================

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    print_error "This script should not be run as root"
    exit 1
fi

# Run main installation
main "$@"
