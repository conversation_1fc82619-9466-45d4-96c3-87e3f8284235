#!/bin/bash
# =============================================================================
# AI Terminal - Fully Automatic WSL Installation Script
# =============================================================================
# This script automatically installs and configures AI Terminal in WSL
# Compatible with: Ubuntu, Debian, and other Debian-based WSL distributions
# 
# Usage: 
#   1. Copy this script to your WSL environment
#   2. Make it executable: chmod +x auto_install_wsl.sh
#   3. Run it: ./auto_install_wsl.sh
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-terminal"
INSTALL_DIR="$HOME/$PROJECT_NAME"
GLOBAL_INSTALL_DIR="/usr/local/bin"
WINDOWS_PROJECT_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
PYTHON_VERSION="3.11"
CONFIG_FILE="$HOME/.ai-terminal-install-state"
FORCE_REINSTALL=false

# Version tracking
SCRIPT_VERSION="2.1"
MIN_PYTHON_VERSION="3.11"

# Global installation flag
INSTALL_GLOBALLY=true

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🚀 AI Terminal WSL Auto-Installer                       ║"
    echo "║                                                                              ║"
    echo "║  This script will automatically install and configure AI Terminal in WSL    ║"
    echo "║  • Install Python 3.11+ and Poetry                                         ║"
    echo "║  • Copy project files from Windows                                          ║"
    echo "║  • Install all dependencies                                                 ║"
    echo "║  • Run interactive setup wizard                                             ║"
    echo "║  • Create convenient shortcuts                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}[STEP] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# State Management Functions
# =============================================================================

save_install_state() {
    local component="$1"
    local version="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create or update state file
    touch "$CONFIG_FILE"

    # Remove old entry for this component
    grep -v "^$component=" "$CONFIG_FILE" > "${CONFIG_FILE}.tmp" 2>/dev/null || true
    mv "${CONFIG_FILE}.tmp" "$CONFIG_FILE" 2>/dev/null || true

    # Add new entry
    echo "$component=$version|$timestamp" >> "$CONFIG_FILE"
}

get_install_state() {
    local component="$1"

    if [[ -f "$CONFIG_FILE" ]]; then
        grep "^$component=" "$CONFIG_FILE" | cut -d'=' -f2 | cut -d'|' -f1
    fi
}

is_component_installed() {
    local component="$1"
    local required_version="$2"
    local installed_version=$(get_install_state "$component")

    if [[ -n "$installed_version" ]]; then
        if [[ -z "$required_version" ]] || [[ "$installed_version" == "$required_version" ]]; then
            return 0
        fi
    fi
    return 1
}

check_python_version() {
    # First try the specific Python version
    if check_command "python$PYTHON_VERSION"; then
        local version=$(python$PYTHON_VERSION --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        echo "$version"
        return 0
    fi

    # Fallback to system Python 3 (for Ubuntu 24.04 with Python 3.12)
    if check_command "python3"; then
        local version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        # Check if version is 3.11+ (compatible)
        local major=$(echo "$version" | cut -d. -f1)
        local minor=$(echo "$version" | cut -d. -f2)
        if [[ "$major" -eq 3 && "$minor" -ge 11 ]]; then
            echo "$version"
            return 0
        fi
    fi

    return 1
}

check_project_needs_update() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        return 0  # Needs installation
    fi

    # Check if Windows project is newer
    if [[ -d "$WINDOWS_PROJECT_PATH" ]]; then
        local windows_time=$(stat -c %Y "$WINDOWS_PROJECT_PATH" 2>/dev/null || echo 0)
        local local_time=$(stat -c %Y "$INSTALL_DIR" 2>/dev/null || echo 0)

        if [[ $windows_time -gt $local_time ]]; then
            return 0  # Needs update
        fi
    fi

    return 1  # No update needed
}

detect_existing_installation() {
    print_step "Detecting existing installation..."

    local needs_update=false

    # Check system packages
    if ! is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_info "System packages need updating"
        needs_update=true
    else
        print_success "System packages are up to date"
    fi

    # Check Python
    if ! check_python_version >/dev/null 2>&1; then
        print_info "Python $PYTHON_VERSION needs installation"
        needs_update=true
    else
        local py_version=$(check_python_version)
        print_success "Python $py_version is installed"
        save_install_state "python" "$py_version"
    fi

    # Check Poetry
    if ! check_command "poetry"; then
        print_info "Poetry needs installation"
        needs_update=true
    else
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version is installed"
        save_install_state "poetry" "$poetry_version"
    fi

    # Check project
    if check_project_needs_update; then
        print_info "AI Terminal project needs update"
        needs_update=true
    else
        print_success "AI Terminal project is up to date"
    fi

    # Check dependencies
    if [[ -d "$INSTALL_DIR" ]]; then
        cd "$INSTALL_DIR"
        if [[ -f "pyproject.toml" ]] && check_command "poetry"; then
            if ! poetry check >/dev/null 2>&1; then
                print_info "Project dependencies need update"
                needs_update=true
            else
                print_success "Project dependencies are satisfied"
            fi
        fi
    fi

    if [[ "$needs_update" == "false" ]]; then
        print_success "All components are up to date!"
        return 1  # No update needed
    fi

    return 0  # Update needed
}

# =============================================================================
# Installation Functions
# =============================================================================

check_wsl() {
    print_step "Checking WSL environment..."
    
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft\|WSL" /proc/version; then
        print_error "This script must be run in WSL (Windows Subsystem for Linux)"
        exit 1
    fi
    
    print_success "WSL environment detected"
}

check_sudo() {
    print_step "Checking sudo access..."

    # Test sudo access
    if sudo -n true 2>/dev/null; then
        print_success "Sudo access confirmed"
        return 0
    fi

    print_info "This script requires sudo access for system package installation"
    print_info "You may be prompted for your password once"

    # Prompt for sudo password and cache it
    if sudo -v; then
        print_success "Sudo access granted"
        return 0
    else
        print_error "Sudo access required. Please run: sudo -v"
        exit 1
    fi
}

update_system() {
    if is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_success "System packages already updated today"
        return
    fi

    print_step "Updating system packages..."

    # Update package lists
    if sudo apt update -qq; then
        print_info "Package lists updated"
    else
        print_warning "Package list update had issues, continuing..."
    fi

    # Upgrade packages (non-interactive)
    if sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y -qq; then
        print_success "System packages updated"
    else
        print_warning "Some packages couldn't be upgraded, continuing..."
    fi

    save_install_state "system_packages" "$(date +%Y-%m-%d)"
}

install_dependencies() {
    if is_component_installed "system_deps" "1.0"; then
        print_success "System dependencies already installed"
        return
    fi

    print_step "Installing system dependencies..."

    # Install dependencies non-interactively
    if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
        software-properties-common \
        build-essential \
        curl \
        git \
        wget \
        unzip \
        tree \
        htop \
        jq \
        libssl-dev \
        libffi-dev \
        python3-dev \
        pkg-config \
        ca-certificates \
        gnupg \
        lsb-release; then
        save_install_state "system_deps" "1.0"
        print_success "System dependencies installed"
    else
        print_warning "Some dependencies may have failed to install, continuing..."
        save_install_state "system_deps" "1.0"
    fi
}

install_python() {
    if check_python_version >/dev/null 2>&1; then
        local py_version=$(check_python_version)
        print_success "Python $py_version already installed"
        save_install_state "python" "$py_version"
        return
    fi

    print_step "Installing Python $PYTHON_VERSION..."

    # Check if we're on Ubuntu 24.04 (Noble) - Python 3.12 is default
    local ubuntu_version=$(lsb_release -rs 2>/dev/null || echo "unknown")
    if [[ "$ubuntu_version" == "24.04" ]]; then
        print_info "Ubuntu 24.04 detected - using system Python 3.12 (compatible)"

        # Install Python 3.12 and related packages (default in Ubuntu 24.04)
        if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
            python3 \
            python3-venv \
            python3-pip \
            python3-dev \
            python3-distutils-extra; then
            print_info "Python 3.12 packages installed"
        else
            print_warning "Some Python packages may have failed, continuing..."
        fi

        # Create symlinks for convenience
        sudo ln -sf /usr/bin/python3 /usr/local/bin/python 2>/dev/null || true

        # Verify installation
        if python3 --version >/dev/null 2>&1; then
            local py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            save_install_state "python" "$py_version"
            print_success "Python $py_version installed"
            return
        fi
    fi

    # For other Ubuntu versions or if 3.12 installation failed, try deadsnakes PPA
    print_info "Adding deadsnakes PPA for Python $PYTHON_VERSION..."
    if sudo add-apt-repository -y ppa:deadsnakes/ppa; then
        print_info "PPA added successfully"
    else
        print_warning "PPA addition had issues, continuing..."
    fi

    # Update package lists
    sudo apt update -qq

    # Install Python and related packages (try without pip first)
    print_info "Installing Python $PYTHON_VERSION packages..."
    local python_packages=(
        "python$PYTHON_VERSION"
        "python$PYTHON_VERSION-venv"
        "python$PYTHON_VERSION-dev"
        "python$PYTHON_VERSION-distutils"
    )

    # Try to install packages one by one
    local installed_packages=()
    for package in "${python_packages[@]}"; do
        if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq "$package" 2>/dev/null; then
            installed_packages+=("$package")
            print_info "Installed: $package"
        else
            print_warning "Failed to install: $package"
        fi
    done

    # Install pip separately using get-pip if package installation failed
    if ! command -v "python$PYTHON_VERSION" >/dev/null 2>&1; then
        print_error "Python $PYTHON_VERSION installation failed"

        # Fallback to system Python
        print_warning "Falling back to system Python..."
        if python3 --version >/dev/null 2>&1; then
            local py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            save_install_state "python" "$py_version"
            print_success "Using system Python $py_version"
            return
        else
            print_error "No working Python installation found"
            exit 1
        fi
    fi

    # Install pip using get-pip.py if not available
    if ! command -v "python$PYTHON_VERSION" -m pip >/dev/null 2>&1; then
        print_info "Installing pip using get-pip.py..."
        curl -sSL https://bootstrap.pypa.io/get-pip.py | python$PYTHON_VERSION - --user 2>/dev/null || true
    fi

    # Create symlinks for convenience
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python3 2>/dev/null || true
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python 2>/dev/null || true

    # Verify installation
    if check_python_version >/dev/null 2>&1; then
        local py_version=$(check_python_version)
        save_install_state "python" "$py_version"
        print_success "Python $py_version installed"
    else
        # Final fallback to system Python
        if python3 --version >/dev/null 2>&1; then
            local py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            save_install_state "python" "$py_version"
            print_success "Using system Python $py_version"
        else
            print_error "Python installation verification failed"
            exit 1
        fi
    fi
}

install_poetry() {
    if check_command "poetry"; then
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version already installed"
        save_install_state "poetry" "$poetry_version"
        return
    fi

    print_step "Installing Poetry package manager..."

    # Install Poetry
    curl -sSL https://install.python-poetry.org | python3 - > /dev/null 2>&1

    # Add Poetry to PATH
    export PATH="$HOME/.local/bin:$PATH"

    # Add to shell profile
    if ! grep -q "/.local/bin" ~/.bashrc; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi

    local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
    save_install_state "poetry" "$poetry_version"
    print_success "Poetry $poetry_version installed"
}

copy_project() {
    if ! check_project_needs_update; then
        print_success "AI Terminal project is up to date"
        return
    fi

    print_step "Updating AI Terminal project from Windows..."

    # Check if Windows project exists
    if [[ ! -d "$WINDOWS_PROJECT_PATH" ]]; then
        print_error "Windows project not found at: $WINDOWS_PROJECT_PATH"
        print_info "Please ensure the project exists in Windows or update the path in this script"
        exit 1
    fi

    # Backup existing configuration if it exists
    local backup_config=""
    if [[ -f "$INSTALL_DIR/.ai-terminal/config.yaml" ]]; then
        backup_config=$(mktemp)
        cp "$INSTALL_DIR/.ai-terminal/config.yaml" "$backup_config"
        print_info "Backing up existing configuration"
    fi

    # Remove existing installation if it exists
    if [[ -d "$INSTALL_DIR" ]]; then
        print_warning "Removing existing installation..."
        rm -rf "$INSTALL_DIR"
    fi

    # Copy project files
    cp -r "$WINDOWS_PROJECT_PATH" "$INSTALL_DIR"

    # Restore configuration if it was backed up
    if [[ -n "$backup_config" && -f "$backup_config" ]]; then
        mkdir -p "$INSTALL_DIR/.ai-terminal"
        cp "$backup_config" "$INSTALL_DIR/.ai-terminal/config.yaml"
        rm "$backup_config"
        print_info "Restored existing configuration"
    fi

    # Set proper permissions
    chmod -R 755 "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/cli.py" 2>/dev/null || true

    save_install_state "project" "$(date '+%Y-%m-%d %H:%M:%S')"
    print_success "Project updated to $INSTALL_DIR"
}

install_project_dependencies() {
    print_step "Installing project dependencies..."

    cd "$INSTALL_DIR"

    # Determine which Python to use
    local python_cmd="python3"
    if check_command "python$PYTHON_VERSION"; then
        python_cmd="python$PYTHON_VERSION"
    fi

    print_info "Using Python: $python_cmd"

    # Check if pyproject.toml exists (Poetry project)
    if [[ -f "pyproject.toml" ]]; then
        print_info "Using Poetry for dependency management..."
        if poetry install; then
            print_success "Dependencies installed with Poetry"
        else
            print_warning "Poetry installation failed, trying pip fallback..."
            # Fallback to pip installation
            $python_cmd -m venv venv
            source venv/bin/activate
            pip install --upgrade pip
            pip install rich typer pydantic aiohttp asyncio
            print_success "Basic dependencies installed with pip"
        fi
    elif [[ -f "requirements.txt" ]]; then
        print_info "Using pip for dependency management..."
        $python_cmd -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        if pip install -r requirements.txt; then
            print_success "Dependencies installed with pip"
        else
            print_warning "Some dependencies failed to install, continuing..."
        fi
    else
        print_warning "No dependency file found, installing common packages..."
        $python_cmd -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        if pip install rich typer pydantic aiohttp; then
            print_success "Basic dependencies installed"
        else
            print_warning "Some basic dependencies failed to install, continuing..."
        fi
    fi
}

create_shortcuts() {
    print_step "Creating convenient shortcuts..."

    # Create activation script
    cat > "$INSTALL_DIR/activate.sh" << 'EOF'
#!/bin/bash
# AI Terminal Environment Activation Script

cd ~/ai-terminal

if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    echo "🚀 Activating Poetry environment..."
    poetry shell
elif [[ -d "venv" ]]; then
    echo "🐍 Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️  No virtual environment found"
fi

echo "✅ AI Terminal environment ready!"
echo ""
echo "🎯 Available commands:"
echo "  python cli.py chat          - Start interactive chat"
echo "  python cli.py quick 'msg'   - Send quick message"
echo "  python cli.py health        - Check system health"
echo "  python cli.py config --show - Show configuration"
echo "  python cli.py setup         - Run setup wizard"
echo ""

exec bash
EOF

    chmod +x "$INSTALL_DIR/activate.sh"

    # Create global wrapper scripts
    if [[ "$INSTALL_GLOBALLY" == "true" ]]; then
        print_info "Installing global commands..."

        # Create global ait command
        sudo tee "$GLOBAL_INSTALL_DIR/ait" > /dev/null << EOF
#!/bin/bash
cd ~/ai-terminal && bash activate.sh
EOF

        # Create global ai-chat command
        sudo tee "$GLOBAL_INSTALL_DIR/ai-chat" > /dev/null << 'EOF'
#!/bin/bash
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run python cli.py chat "$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && python cli.py chat "$@"
elif command -v python3.11 &> /dev/null; then
    python3.11 cli.py chat "$@"
else
    python3 cli.py chat "$@"
fi
EOF

        # Create global ai-quick command
        sudo tee "$GLOBAL_INSTALL_DIR/ai-quick" > /dev/null << 'EOF'
#!/bin/bash
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run python cli.py quick "$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && python cli.py quick "$@"
elif command -v python3.11 &> /dev/null; then
    python3.11 cli.py quick "$@"
else
    python3 cli.py quick "$@"
fi
EOF

        # Create global ai-health command
        sudo tee "$GLOBAL_INSTALL_DIR/ai-health" > /dev/null << 'EOF'
#!/bin/bash
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run python cli.py health "$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && python cli.py health "$@"
elif command -v python3.11 &> /dev/null; then
    python3.11 cli.py health "$@"
else
    python3 cli.py health "$@"
fi
EOF

        # Create global ai-config command
        sudo tee "$GLOBAL_INSTALL_DIR/ai-config" > /dev/null << 'EOF'
#!/bin/bash
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run python cli.py config --show "$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && python cli.py config --show "$@"
elif command -v python3.11 &> /dev/null; then
    python3.11 cli.py config --show "$@"
else
    python3 cli.py config --show "$@"
fi
EOF

        # Create global ai-setup command
        sudo tee "$GLOBAL_INSTALL_DIR/ai-setup" > /dev/null << 'EOF'
#!/bin/bash
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run python cli.py setup "$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && python cli.py setup "$@"
elif command -v python3.11 &> /dev/null; then
    python3.11 cli.py setup "$@"
else
    python3 cli.py setup "$@"
fi
EOF

        # Make all global commands executable
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ait"
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ai-chat"
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ai-quick"
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ai-health"
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ai-config"
        sudo chmod +x "$GLOBAL_INSTALL_DIR/ai-setup"

        print_success "Global commands installed to $GLOBAL_INSTALL_DIR"
    fi

    # Add aliases to bashrc as backup
    if ! grep -q "ai-terminal aliases" ~/.bashrc; then
        cat >> ~/.bashrc << EOF

# ai-terminal aliases (backup to global commands)
alias ait-local='cd ~/ai-terminal && bash activate.sh'
alias ai-chat-local='cd ~/ai-terminal && python cli.py chat'
alias ai-quick-local='cd ~/ai-terminal && python cli.py quick'
alias ai-health-local='cd ~/ai-terminal && python cli.py health'
alias ai-config-local='cd ~/ai-terminal && python cli.py config --show'
alias ai-setup-local='cd ~/ai-terminal && python cli.py setup'
EOF
    fi

    print_success "Shortcuts and global commands created"
}

test_installation() {
    print_step "Testing installation..."

    cd "$INSTALL_DIR"

    # Determine which Python to use
    local python_cmd="python3"
    if check_command "python$PYTHON_VERSION"; then
        python_cmd="python$PYTHON_VERSION"
    fi

    # Test basic CLI functionality
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        if poetry run python cli.py --help &> /dev/null; then
            print_success "CLI test passed with Poetry"
        else
            print_warning "Poetry CLI test failed, trying fallback..."
            if $python_cmd cli.py --help &> /dev/null; then
                print_success "CLI test passed with $python_cmd"
            else
                print_error "CLI test failed"
                return 1
            fi
        fi
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        if python cli.py --help &> /dev/null; then
            print_success "CLI test passed with venv"
        else
            print_warning "venv CLI test failed, trying fallback..."
            if $python_cmd cli.py --help &> /dev/null; then
                print_success "CLI test passed with $python_cmd"
            else
                print_error "CLI test failed"
                return 1
            fi
        fi
    else
        if $python_cmd cli.py --help &> /dev/null; then
            print_success "CLI test passed with $python_cmd"
        else
            print_error "CLI test failed with $python_cmd"
            return 1
        fi
    fi
}

run_setup_wizard() {
    print_step "Launching AI Terminal setup wizard..."

    cd "$INSTALL_DIR"

    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                          🎯 Setup Wizard Starting                           ║"
    echo "║                                                                              ║"
    echo "║  The interactive setup wizard will now guide you through:                   ║"
    echo "║  • Selecting your AI provider (OpenAI, Deepseek, Anthropic, Ollama)        ║"
    echo "║  • Configuring API keys (with multiple input methods)                       ║"
    echo "║  • Setting preferences (temperature, tokens, security, theme)               ║"
    echo "║                                                                              ║"
    echo "║  💡 Tip: Have your API key ready for easy pasting!                         ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo "Press Enter to start the setup wizard..."
    read

    # Run setup wizard
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        poetry run python cli.py setup
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        python cli.py setup
    else
        python3 cli.py setup
    fi
}

show_completion() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🎉 Installation Complete! 🎉                           ║"
    echo "║                                                                              ║"
    echo "║  AI Terminal has been successfully installed and configured in WSL!         ║"
    echo "║  🧠 Smart updates enabled - future runs will only update what's needed      ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}📍 Installation Location:${NC} $INSTALL_DIR"
    echo -e "${CYAN}📊 State File:${NC} $CONFIG_FILE"
    echo ""
    echo -e "${YELLOW}🚀 Global Commands (Available Anywhere):${NC}"
    echo -e "  ${BLUE}ait${NC}                    - Activate AI Terminal environment"
    echo -e "  ${BLUE}ai-chat${NC}                - Start interactive chat"
    echo -e "  ${BLUE}ai-quick 'message'${NC}     - Send quick message"
    echo -e "  ${BLUE}ai-health${NC}              - Check system health"
    echo -e "  ${BLUE}ai-config${NC}              - Show configuration"
    echo -e "  ${BLUE}ai-setup${NC}               - Re-run setup wizard"
    echo ""
    echo -e "${YELLOW}🔧 Installer Commands:${NC}"
    echo -e "  ${BLUE}./auto_install_wsl.sh --check${NC}      - Check what needs updating"
    echo -e "  ${BLUE}./auto_install_wsl.sh --update${NC}     - Update components only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --setup-only${NC} - Run setup wizard only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --force${NC}      - Force complete reinstall"
    echo ""
    echo -e "${YELLOW}📚 Manual Commands:${NC}"
    echo -e "  ${BLUE}cd ~/ai-terminal${NC}       - Navigate to project"
    echo -e "  ${BLUE}poetry shell${NC}           - Activate Poetry environment"
    echo -e "  ${BLUE}python cli.py chat${NC}     - Start chat session"
    echo ""
    echo -e "${GREEN}✨ Enjoy using AI Terminal! ✨${NC}"
    echo ""
    echo -e "${PURPLE}💡 Tip: Restart your terminal or run 'source ~/.bashrc' to use the new aliases${NC}"
    echo -e "${PURPLE}🧠 Smart Feature: Re-running this script will only update what's changed!${NC}"
}

# =============================================================================
# Main Installation Process
# =============================================================================

show_help() {
    echo -e "${CYAN}AI Terminal WSL Auto-Installer v$SCRIPT_VERSION${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -f, --force         Force complete reinstallation"
    echo "  -u, --update        Update only (skip setup wizard)"
    echo "  -s, --setup-only    Run setup wizard only"
    echo "  -c, --check         Check installation status"
    echo "  --version           Show version information"
    echo ""
    echo "Examples:"
    echo "  $0                  # Smart install/update (default)"
    echo "  $0 --force          # Force complete reinstallation"
    echo "  $0 --update         # Update components only"
    echo "  $0 --setup-only     # Run setup wizard only"
    echo "  $0 --check          # Check what needs updating"
}

check_status() {
    print_header
    print_step "Checking AI Terminal installation status..."

    if detect_existing_installation; then
        echo -e "${YELLOW}Some components need updating${NC}"
        exit 0
    else
        echo -e "${GREEN}All components are up to date!${NC}"
        exit 0
    fi
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_REINSTALL=true
                shift
                ;;
            -u|--update)
                UPDATE_ONLY=true
                shift
                ;;
            -s|--setup-only)
                SETUP_ONLY=true
                shift
                ;;
            -c|--check)
                check_status
                ;;
            --version)
                echo "AI Terminal WSL Auto-Installer v$SCRIPT_VERSION"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    print_header

    # Handle setup-only mode
    if [[ "$SETUP_ONLY" == "true" ]]; then
        if [[ ! -d "$INSTALL_DIR" ]]; then
            print_error "AI Terminal is not installed. Run without --setup-only first."
            exit 1
        fi
        run_setup_wizard
        exit 0
    fi

    # Check if update is needed (unless forced)
    if [[ "$FORCE_REINSTALL" != "true" ]]; then
        if ! detect_existing_installation; then
            if [[ "$UPDATE_ONLY" == "true" ]]; then
                print_success "All components are up to date!"
                exit 0
            else
                print_success "All components are up to date!"
                echo ""
                echo -e "${CYAN}Options:${NC}"
                echo -e "  ${BLUE}$0 --setup-only${NC}     - Run setup wizard"
                echo -e "  ${BLUE}$0 --force${NC}          - Force reinstallation"
                echo -e "  ${BLUE}ai-chat${NC}             - Start using AI Terminal"
                exit 0
            fi
        fi
    else
        print_warning "Force reinstallation requested - removing state file"
        rm -f "$CONFIG_FILE"
    fi

    echo "Starting intelligent installation/update..."
    sleep 2

    # Core installation steps (smart - only run what's needed)
    check_wsl
    check_sudo
    update_system
    install_dependencies
    install_python
    install_poetry
    copy_project
    install_project_dependencies
    create_shortcuts
    test_installation

    # Interactive setup (skip if update-only)
    if [[ "$UPDATE_ONLY" != "true" ]]; then
        # Check if setup has been run before
        if [[ ! -f "$HOME/.config/ai-terminal/config.yaml" ]] && [[ ! -f "$HOME/.ai-terminal/config.yaml" ]] && [[ ! -f "$INSTALL_DIR/.ai-terminal/config.yaml" ]]; then
            print_info "No existing configuration found. Running setup wizard..."
            run_setup_wizard
        else
            print_success "Configuration exists. AI Terminal is ready to use!"
            print_info "Use 'ai-setup' or '$0 --setup-only' to reconfigure."
        fi
    fi

    # Completion
    show_completion

    # Test global commands
    print_step "Testing global commands..."
    if command -v ait >/dev/null 2>&1; then
        print_success "Global commands are working!"
    else
        print_warning "Global commands may need a shell restart"
        print_info "Try opening a new terminal or run: hash -r"
    fi

    # Source bashrc to enable aliases
    echo "Reloading shell configuration..."
    source ~/.bashrc 2>/dev/null || true
    hash -r 2>/dev/null || true  # Refresh command hash table

    echo ""
    echo -e "${GREEN}🎯 Ready to use! Try: ${BLUE}ait${NC} or ${BLUE}ai-chat${NC}"
    echo -e "${CYAN}💡 If commands aren't found, open a new terminal window${NC}"
}

# =============================================================================
# Error Handling
# =============================================================================

trap 'print_error "Installation failed at line $LINENO. Check the error above."' ERR

# =============================================================================
# Script Execution
# =============================================================================

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    print_error "This script should not be run as root"
    exit 1
fi

# Run main installation
main "$@"
