#!/bin/bash
# =============================================================================
# AI Terminal - Fully Automatic WSL Installation Script
# =============================================================================
# This script automatically installs and configures AI Terminal in WSL
# Compatible with: Ubuntu, Debian, and other Debian-based WSL distributions
# 
# Usage: 
#   1. Copy this script to your WSL environment
#   2. Make it executable: chmod +x auto_install_wsl.sh
#   3. Run it: ./auto_install_wsl.sh
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-terminal"
INSTALL_DIR="$HOME/$PROJECT_NAME"
WINDOWS_PROJECT_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
PYTHON_VERSION="3.11"
CONFIG_FILE="$HOME/.ai-terminal-install-state"
FORCE_REINSTALL=false

# Version tracking
SCRIPT_VERSION="2.0"
MIN_PYTHON_VERSION="3.11"

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    clear
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🚀 AI Terminal WSL Auto-Installer                       ║"
    echo "║                                                                              ║"
    echo "║  This script will automatically install and configure AI Terminal in WSL    ║"
    echo "║  • Install Python 3.11+ and Poetry                                         ║"
    echo "║  • Copy project files from Windows                                          ║"
    echo "║  • Install all dependencies                                                 ║"
    echo "║  • Run interactive setup wizard                                             ║"
    echo "║  • Create convenient shortcuts                                              ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

print_step() {
    echo -e "${CYAN}[STEP] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# State Management Functions
# =============================================================================

save_install_state() {
    local component="$1"
    local version="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create or update state file
    touch "$CONFIG_FILE"

    # Remove old entry for this component
    grep -v "^$component=" "$CONFIG_FILE" > "${CONFIG_FILE}.tmp" 2>/dev/null || true
    mv "${CONFIG_FILE}.tmp" "$CONFIG_FILE" 2>/dev/null || true

    # Add new entry
    echo "$component=$version|$timestamp" >> "$CONFIG_FILE"
}

get_install_state() {
    local component="$1"

    if [[ -f "$CONFIG_FILE" ]]; then
        grep "^$component=" "$CONFIG_FILE" | cut -d'=' -f2 | cut -d'|' -f1
    fi
}

is_component_installed() {
    local component="$1"
    local required_version="$2"
    local installed_version=$(get_install_state "$component")

    if [[ -n "$installed_version" ]]; then
        if [[ -z "$required_version" ]] || [[ "$installed_version" == "$required_version" ]]; then
            return 0
        fi
    fi
    return 1
}

check_python_version() {
    if check_command "python$PYTHON_VERSION"; then
        local version=$(python$PYTHON_VERSION --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        echo "$version"
        return 0
    fi
    return 1
}

check_project_needs_update() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        return 0  # Needs installation
    fi

    # Check if Windows project is newer
    if [[ -d "$WINDOWS_PROJECT_PATH" ]]; then
        local windows_time=$(stat -c %Y "$WINDOWS_PROJECT_PATH" 2>/dev/null || echo 0)
        local local_time=$(stat -c %Y "$INSTALL_DIR" 2>/dev/null || echo 0)

        if [[ $windows_time -gt $local_time ]]; then
            return 0  # Needs update
        fi
    fi

    return 1  # No update needed
}

detect_existing_installation() {
    print_step "Detecting existing installation..."

    local needs_update=false

    # Check system packages
    if ! is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_info "System packages need updating"
        needs_update=true
    else
        print_success "System packages are up to date"
    fi

    # Check Python
    if ! check_python_version >/dev/null 2>&1; then
        print_info "Python $PYTHON_VERSION needs installation"
        needs_update=true
    else
        local py_version=$(check_python_version)
        print_success "Python $py_version is installed"
        save_install_state "python" "$py_version"
    fi

    # Check Poetry
    if ! check_command "poetry"; then
        print_info "Poetry needs installation"
        needs_update=true
    else
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version is installed"
        save_install_state "poetry" "$poetry_version"
    fi

    # Check project
    if check_project_needs_update; then
        print_info "AI Terminal project needs update"
        needs_update=true
    else
        print_success "AI Terminal project is up to date"
    fi

    # Check dependencies
    if [[ -d "$INSTALL_DIR" ]]; then
        cd "$INSTALL_DIR"
        if [[ -f "pyproject.toml" ]] && check_command "poetry"; then
            if ! poetry check >/dev/null 2>&1; then
                print_info "Project dependencies need update"
                needs_update=true
            else
                print_success "Project dependencies are satisfied"
            fi
        fi
    fi

    if [[ "$needs_update" == "false" ]]; then
        print_success "All components are up to date!"
        return 1  # No update needed
    fi

    return 0  # Update needed
}

# =============================================================================
# Installation Functions
# =============================================================================

check_wsl() {
    print_step "Checking WSL environment..."
    
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft\|WSL" /proc/version; then
        print_error "This script must be run in WSL (Windows Subsystem for Linux)"
        exit 1
    fi
    
    print_success "WSL environment detected"
}

update_system() {
    if is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_success "System packages already updated today"
        return
    fi

    print_step "Updating system packages..."

    sudo apt update -qq > /dev/null 2>&1
    sudo apt upgrade -y -qq > /dev/null 2>&1

    save_install_state "system_packages" "$(date +%Y-%m-%d)"
    print_success "System packages updated"
}

install_dependencies() {
    if is_component_installed "system_deps" "1.0"; then
        print_success "System dependencies already installed"
        return
    fi

    print_step "Installing system dependencies..."

    sudo apt install -y -qq \
        software-properties-common \
        build-essential \
        curl \
        git \
        wget \
        unzip \
        tree \
        htop \
        jq \
        libssl-dev \
        libffi-dev \
        python3-dev \
        pkg-config \
        ca-certificates \
        gnupg \
        lsb-release > /dev/null 2>&1

    save_install_state "system_deps" "1.0"
    print_success "System dependencies installed"
}

install_python() {
    if check_python_version >/dev/null 2>&1; then
        local py_version=$(check_python_version)
        print_success "Python $py_version already installed"
        save_install_state "python" "$py_version"
        return
    fi

    print_step "Installing Python $PYTHON_VERSION..."

    # Add deadsnakes PPA for latest Python versions
    sudo add-apt-repository -y ppa:deadsnakes/ppa > /dev/null 2>&1
    sudo apt update -qq > /dev/null 2>&1

    # Install Python and related packages
    sudo apt install -y -qq \
        python$PYTHON_VERSION \
        python$PYTHON_VERSION-venv \
        python$PYTHON_VERSION-pip \
        python$PYTHON_VERSION-dev \
        python$PYTHON_VERSION-distutils > /dev/null 2>&1

    # Create symlinks for convenience
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python3 2>/dev/null || true
    sudo ln -sf /usr/bin/python$PYTHON_VERSION /usr/local/bin/python 2>/dev/null || true

    local py_version=$(check_python_version)
    save_install_state "python" "$py_version"
    print_success "Python $py_version installed"
}

install_poetry() {
    if check_command "poetry"; then
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version already installed"
        save_install_state "poetry" "$poetry_version"
        return
    fi

    print_step "Installing Poetry package manager..."

    # Install Poetry
    curl -sSL https://install.python-poetry.org | python3 - > /dev/null 2>&1

    # Add Poetry to PATH
    export PATH="$HOME/.local/bin:$PATH"

    # Add to shell profile
    if ! grep -q "/.local/bin" ~/.bashrc; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi

    local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
    save_install_state "poetry" "$poetry_version"
    print_success "Poetry $poetry_version installed"
}

copy_project() {
    if ! check_project_needs_update; then
        print_success "AI Terminal project is up to date"
        return
    fi

    print_step "Updating AI Terminal project from Windows..."

    # Check if Windows project exists
    if [[ ! -d "$WINDOWS_PROJECT_PATH" ]]; then
        print_error "Windows project not found at: $WINDOWS_PROJECT_PATH"
        print_info "Please ensure the project exists in Windows or update the path in this script"
        exit 1
    fi

    # Backup existing configuration if it exists
    local backup_config=""
    if [[ -f "$INSTALL_DIR/.ai-terminal/config.yaml" ]]; then
        backup_config=$(mktemp)
        cp "$INSTALL_DIR/.ai-terminal/config.yaml" "$backup_config"
        print_info "Backing up existing configuration"
    fi

    # Remove existing installation if it exists
    if [[ -d "$INSTALL_DIR" ]]; then
        print_warning "Removing existing installation..."
        rm -rf "$INSTALL_DIR"
    fi

    # Copy project files
    cp -r "$WINDOWS_PROJECT_PATH" "$INSTALL_DIR"

    # Restore configuration if it was backed up
    if [[ -n "$backup_config" && -f "$backup_config" ]]; then
        mkdir -p "$INSTALL_DIR/.ai-terminal"
        cp "$backup_config" "$INSTALL_DIR/.ai-terminal/config.yaml"
        rm "$backup_config"
        print_info "Restored existing configuration"
    fi

    # Set proper permissions
    chmod -R 755 "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/cli.py" 2>/dev/null || true

    save_install_state "project" "$(date '+%Y-%m-%d %H:%M:%S')"
    print_success "Project updated to $INSTALL_DIR"
}

install_project_dependencies() {
    print_step "Installing project dependencies..."
    
    cd "$INSTALL_DIR"
    
    # Check if pyproject.toml exists (Poetry project)
    if [[ -f "pyproject.toml" ]]; then
        print_info "Using Poetry for dependency management..."
        poetry install > /dev/null 2>&1
        print_success "Dependencies installed with Poetry"
    elif [[ -f "requirements.txt" ]]; then
        print_info "Using pip for dependency management..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip > /dev/null 2>&1
        pip install -r requirements.txt > /dev/null 2>&1
        print_success "Dependencies installed with pip"
    else
        print_warning "No dependency file found, installing common packages..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip > /dev/null 2>&1
        pip install rich typer pydantic aiohttp > /dev/null 2>&1
        print_success "Basic dependencies installed"
    fi
}

create_shortcuts() {
    print_step "Creating convenient shortcuts..."

    # Create activation script
    cat > "$INSTALL_DIR/activate.sh" << 'EOF'
#!/bin/bash
# AI Terminal Environment Activation Script

cd ~/ai-terminal

if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    echo "🚀 Activating Poetry environment..."
    poetry shell
elif [[ -d "venv" ]]; then
    echo "🐍 Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️  No virtual environment found"
fi

echo "✅ AI Terminal environment ready!"
echo ""
echo "🎯 Available commands:"
echo "  python cli.py chat          - Start interactive chat"
echo "  python cli.py quick 'msg'   - Send quick message"
echo "  python cli.py health        - Check system health"
echo "  python cli.py config --show - Show configuration"
echo "  python cli.py setup         - Run setup wizard"
echo ""

exec bash
EOF

    chmod +x "$INSTALL_DIR/activate.sh"

    # Add aliases to bashrc
    if ! grep -q "ai-terminal aliases" ~/.bashrc; then
        cat >> ~/.bashrc << EOF

# ai-terminal aliases
alias ait='cd ~/ai-terminal && bash activate.sh'
alias ai-chat='cd ~/ai-terminal && python cli.py chat'
alias ai-quick='cd ~/ai-terminal && python cli.py quick'
alias ai-health='cd ~/ai-terminal && python cli.py health'
alias ai-config='cd ~/ai-terminal && python cli.py config --show'
alias ai-setup='cd ~/ai-terminal && python cli.py setup'
EOF
    fi

    print_success "Shortcuts created"
}

test_installation() {
    print_step "Testing installation..."

    cd "$INSTALL_DIR"

    # Test basic CLI functionality
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        if poetry run python cli.py --help &> /dev/null; then
            print_success "CLI test passed with Poetry"
        else
            print_error "CLI test failed with Poetry"
            return 1
        fi
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        if python cli.py --help &> /dev/null; then
            print_success "CLI test passed with venv"
        else
            print_error "CLI test failed with venv"
            return 1
        fi
    else
        if python3 cli.py --help &> /dev/null; then
            print_success "CLI test passed with system Python"
        else
            print_error "CLI test failed"
            return 1
        fi
    fi
}

run_setup_wizard() {
    print_step "Launching AI Terminal setup wizard..."

    cd "$INSTALL_DIR"

    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                          🎯 Setup Wizard Starting                           ║"
    echo "║                                                                              ║"
    echo "║  The interactive setup wizard will now guide you through:                   ║"
    echo "║  • Selecting your AI provider (OpenAI, Deepseek, Anthropic, Ollama)        ║"
    echo "║  • Configuring API keys (with multiple input methods)                       ║"
    echo "║  • Setting preferences (temperature, tokens, security, theme)               ║"
    echo "║                                                                              ║"
    echo "║  💡 Tip: Have your API key ready for easy pasting!                         ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo "Press Enter to start the setup wizard..."
    read

    # Run setup wizard
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        poetry run python cli.py setup
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        python cli.py setup
    else
        python3 cli.py setup
    fi
}

show_completion() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🎉 Installation Complete! 🎉                           ║"
    echo "║                                                                              ║"
    echo "║  AI Terminal has been successfully installed and configured in WSL!         ║"
    echo "║  🧠 Smart updates enabled - future runs will only update what's needed      ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}📍 Installation Location:${NC} $INSTALL_DIR"
    echo -e "${CYAN}📊 State File:${NC} $CONFIG_FILE"
    echo ""
    echo -e "${YELLOW}🚀 Quick Start Commands:${NC}"
    echo -e "  ${BLUE}ait${NC}                    - Activate AI Terminal environment"
    echo -e "  ${BLUE}ai-chat${NC}                - Start interactive chat"
    echo -e "  ${BLUE}ai-quick 'message'${NC}     - Send quick message"
    echo -e "  ${BLUE}ai-health${NC}              - Check system health"
    echo -e "  ${BLUE}ai-config${NC}              - Show configuration"
    echo -e "  ${BLUE}ai-setup${NC}               - Re-run setup wizard"
    echo ""
    echo -e "${YELLOW}🔧 Installer Commands:${NC}"
    echo -e "  ${BLUE}./auto_install_wsl.sh --check${NC}      - Check what needs updating"
    echo -e "  ${BLUE}./auto_install_wsl.sh --update${NC}     - Update components only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --setup-only${NC} - Run setup wizard only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --force${NC}      - Force complete reinstall"
    echo ""
    echo -e "${YELLOW}📚 Manual Commands:${NC}"
    echo -e "  ${BLUE}cd ~/ai-terminal${NC}       - Navigate to project"
    echo -e "  ${BLUE}poetry shell${NC}           - Activate Poetry environment"
    echo -e "  ${BLUE}python cli.py chat${NC}     - Start chat session"
    echo ""
    echo -e "${GREEN}✨ Enjoy using AI Terminal! ✨${NC}"
    echo ""
    echo -e "${PURPLE}💡 Tip: Restart your terminal or run 'source ~/.bashrc' to use the new aliases${NC}"
    echo -e "${PURPLE}🧠 Smart Feature: Re-running this script will only update what's changed!${NC}"
}

# =============================================================================
# Main Installation Process
# =============================================================================

show_help() {
    echo -e "${CYAN}AI Terminal WSL Auto-Installer v$SCRIPT_VERSION${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -f, --force         Force complete reinstallation"
    echo "  -u, --update        Update only (skip setup wizard)"
    echo "  -s, --setup-only    Run setup wizard only"
    echo "  -c, --check         Check installation status"
    echo "  --version           Show version information"
    echo ""
    echo "Examples:"
    echo "  $0                  # Smart install/update (default)"
    echo "  $0 --force          # Force complete reinstallation"
    echo "  $0 --update         # Update components only"
    echo "  $0 --setup-only     # Run setup wizard only"
    echo "  $0 --check          # Check what needs updating"
}

check_status() {
    print_header
    print_step "Checking AI Terminal installation status..."

    if detect_existing_installation; then
        echo -e "${YELLOW}Some components need updating${NC}"
        exit 0
    else
        echo -e "${GREEN}All components are up to date!${NC}"
        exit 0
    fi
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_REINSTALL=true
                shift
                ;;
            -u|--update)
                UPDATE_ONLY=true
                shift
                ;;
            -s|--setup-only)
                SETUP_ONLY=true
                shift
                ;;
            -c|--check)
                check_status
                ;;
            --version)
                echo "AI Terminal WSL Auto-Installer v$SCRIPT_VERSION"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    print_header

    # Handle setup-only mode
    if [[ "$SETUP_ONLY" == "true" ]]; then
        if [[ ! -d "$INSTALL_DIR" ]]; then
            print_error "AI Terminal is not installed. Run without --setup-only first."
            exit 1
        fi
        run_setup_wizard
        exit 0
    fi

    # Check if update is needed (unless forced)
    if [[ "$FORCE_REINSTALL" != "true" ]]; then
        if ! detect_existing_installation; then
            if [[ "$UPDATE_ONLY" == "true" ]]; then
                print_success "All components are up to date!"
                exit 0
            else
                print_success "All components are up to date!"
                echo ""
                echo -e "${CYAN}Options:${NC}"
                echo -e "  ${BLUE}$0 --setup-only${NC}     - Run setup wizard"
                echo -e "  ${BLUE}$0 --force${NC}          - Force reinstallation"
                echo -e "  ${BLUE}ai-chat${NC}             - Start using AI Terminal"
                exit 0
            fi
        fi
    else
        print_warning "Force reinstallation requested - removing state file"
        rm -f "$CONFIG_FILE"
    fi

    echo "Starting intelligent installation/update..."
    sleep 2

    # Core installation steps (smart - only run what's needed)
    check_wsl
    update_system
    install_dependencies
    install_python
    install_poetry
    copy_project
    install_project_dependencies
    create_shortcuts
    test_installation

    # Interactive setup (skip if update-only)
    if [[ "$UPDATE_ONLY" != "true" ]]; then
        # Check if setup has been run before
        if [[ ! -f "$HOME/.config/ai-terminal/config.yaml" ]] && [[ ! -f "$HOME/.ai-terminal/config.yaml" ]]; then
            run_setup_wizard
        else
            print_info "Configuration exists. Use --setup-only to reconfigure."
        fi
    fi

    # Completion
    show_completion

    # Source bashrc to enable aliases
    echo "Reloading shell configuration..."
    source ~/.bashrc 2>/dev/null || true

    echo ""
    echo -e "${GREEN}🎯 Ready to use! Try: ${BLUE}ait${NC}"
}

# =============================================================================
# Error Handling
# =============================================================================

trap 'print_error "Installation failed at line $LINENO. Check the error above."' ERR

# =============================================================================
# Script Execution
# =============================================================================

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    print_error "This script should not be run as root"
    exit 1
fi

# Run main installation
main "$@"
