"""
Interactive overlay components for AI Terminal.

Provides modal dialogs, selection screens, and interactive interfaces
for model/provider selection, configuration, and approval workflows.
"""

import asyncio
from typing import List, Dict, Optional, Any, Callable, Tuple
from enum import Enum
from dataclasses import dataclass

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.layout import Layout
from rich.align import Align
from rich.columns import Columns
from rich.progress import Progress, SpinnerColumn, TextColumn

from ai_terminal.config.providers import get_all_providers, ProviderType, ProviderConfig
from ai_terminal.providers.base import BaseProvider
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class OverlayResult(Enum):
    """Result of overlay interaction."""
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    ERROR = "error"


@dataclass
class SelectionItem:
    """Item for selection overlays."""
    id: str
    title: str
    description: str
    metadata: Dict[str, Any] = None


class BaseOverlay:
    """Base class for interactive overlays."""
    
    def __init__(self, console: Optional[Console] = None):
        """Initialize overlay.
        
        Args:
            console: Rich console instance
        """
        self.console = console or Console()
        self.result = OverlayResult.CANCELLED
        self.selected_value = None
    
    def show_header(self, title: str, subtitle: Optional[str] = None):
        """Show overlay header.
        
        Args:
            title: Main title
            subtitle: Optional subtitle
        """
        header_text = Text()
        header_text.append(title, style="bold blue")
        if subtitle:
            header_text.append(f"\n{subtitle}", style="dim")
        
        panel = Panel(
            header_text,
            border_style="blue",
            padding=(1, 2),
        )
        self.console.print(panel)
    
    def show_footer(self, instructions: str = "Use arrow keys to navigate, Enter to select, Esc to cancel"):
        """Show overlay footer.
        
        Args:
            instructions: User instructions
        """
        footer_text = Text(instructions, style="dim")
        self.console.print(Align.center(footer_text))
        self.console.print()


class ProviderSelectionOverlay(BaseOverlay):
    """Interactive provider selection overlay."""
    
    async def show(self, current_provider: Optional[str] = None) -> Tuple[OverlayResult, Optional[str]]:
        """Show provider selection interface.
        
        Args:
            current_provider: Currently selected provider
            
        Returns:
            Tuple of (result, selected_provider)
        """
        self.console.clear()
        self.show_header("Select AI Provider", "Choose your preferred AI provider")
        
        # Get available providers
        providers = get_all_providers()
        provider_items = []
        
        for provider_type, config in providers.items():
            # Check if provider is available (has API key or is local)
            available = self._check_provider_availability(config)
            status = "✅ Available" if available else "❌ Not configured"
            
            # Create description from provider info
            description = self._get_provider_description(config)

            provider_items.append(SelectionItem(
                id=provider_type.value,
                title=config.display_name,
                description=f"{description} - {status}",
                metadata={"config": config, "available": available}
            ))
        
        # Show provider table
        table = Table(title="Available Providers", show_header=True, header_style="bold magenta")
        table.add_column("Provider", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("Status", style="green")
        table.add_column("Models", style="dim")
        
        for i, item in enumerate(provider_items, 1):
            config = item.metadata["config"]
            available = item.metadata["available"]
            
            status_style = "green" if available else "red"
            status_text = "✅ Ready" if available else "❌ Setup needed"
            
            # Show first few models
            model_names = [m.display_name for m in config.models[:3]]
            if len(config.models) > 3:
                model_names.append(f"... +{len(config.models) - 3} more")
            models_text = ", ".join(model_names)
            
            # Highlight current provider
            provider_style = "bold cyan" if item.id == current_provider else "cyan"
            
            table.add_row(
                f"{i}. {item.title}",
                item.description.split(" - ")[0],  # Remove status from description
                f"[{status_style}]{status_text}[/{status_style}]",
                models_text,
                style=provider_style if item.id == current_provider else None
            )
        
        self.console.print(table)
        self.console.print()
        
        # Get user selection
        try:
            choice = IntPrompt.ask(
                "Select provider",
                choices=[str(i) for i in range(1, len(provider_items) + 1)],
                default="1"
            )
            
            selected_item = provider_items[choice - 1]
            
            # Check if provider needs setup
            if not selected_item.metadata["available"]:
                setup_needed = Confirm.ask(
                    f"Provider '{selected_item.title}' needs setup. Configure now?",
                    default=True
                )
                
                if setup_needed:
                    success = await self._setup_provider(selected_item.metadata["config"])
                    if not success:
                        self.console.print("[red]Provider setup failed[/red]")
                        return OverlayResult.ERROR, None
            
            self.result = OverlayResult.CONFIRMED
            self.selected_value = selected_item.id
            
            self.console.print(f"[green]Selected provider: {selected_item.title}[/green]")
            return self.result, self.selected_value
            
        except KeyboardInterrupt:
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return OverlayResult.CANCELLED, None
        except Exception as e:
            logger.error(f"Provider selection error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return OverlayResult.ERROR, None
    
    def _check_provider_availability(self, config: ProviderConfig) -> bool:
        """Check if provider is available (configured).
        
        Args:
            config: Provider configuration
            
        Returns:
            True if provider is available
        """
        import os
        
        # Local providers (like Ollama) don't need API keys
        if config.type == ProviderType.OLLAMA:
            return True
        
        # Check for API key
        if config.api_key_env:
            return bool(os.getenv(config.api_key_env))
        
        return False
    
    async def _setup_provider(self, config: ProviderConfig) -> bool:
        """Setup provider configuration.

        Args:
            config: Provider configuration

        Returns:
            True if setup successful
        """
        self.console.print(f"\n[bold]Setting up {config.display_name}[/bold]")

        if config.api_key_env:
            self.console.print(f"You need to provide an API key for {config.display_name}")
            self.console.print(f"You can either:")
            self.console.print(f"1. Enter it directly here (recommended)")
            self.console.print(f"2. Set the environment variable {config.api_key_env}")

            # Check if already set in environment
            import os
            existing_key = os.getenv(config.api_key_env)
            if existing_key:
                self.console.print(f"[green]API key already found in environment![/green]")
                return True

            # Ask user to enter API key directly
            from rich.prompt import Prompt
            api_key = Prompt.ask(
                f"Enter your {config.display_name} API key",
                password=True,  # Hide the input
                default=""
            )

            if api_key.strip():
                # Set the environment variable for this session
                os.environ[config.api_key_env] = api_key.strip()
                self.console.print("[green]API key configured successfully![/green]")
                return True
            else:
                self.console.print("[red]No API key provided[/red]")
                return False

        return True

    def _get_provider_description(self, config: ProviderConfig) -> str:
        """Get a description for the provider based on its configuration.

        Args:
            config: Provider configuration

        Returns:
            Description string
        """
        descriptions = {
            "openai": "Advanced AI models including GPT-4 and GPT-3.5",
            "deepseek": "High-performance coding models optimized for development",
            "anthropic": "Claude family models with strong reasoning capabilities",
            "ollama": "Local AI models running on your machine"
        }

        return descriptions.get(config.name, f"AI provider with {len(config.models)} models")


class ModelSelectionOverlay(BaseOverlay):
    """Interactive model selection overlay."""
    
    async def show(
        self,
        provider: BaseProvider,
        current_model: Optional[str] = None
    ) -> Tuple[OverlayResult, Optional[str]]:
        """Show model selection interface.
        
        Args:
            provider: AI provider instance
            current_model: Currently selected model
            
        Returns:
            Tuple of (result, selected_model)
        """
        self.console.clear()
        self.show_header(
            f"Select Model for {provider.name.title()}",
            "Choose the AI model for your conversation"
        )
        
        # Show loading spinner while fetching models
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("Fetching available models...", total=None)
            
            try:
                # Get models from provider
                available_models = await provider.list_models()
                progress.update(task, description="Models loaded!")
                
            except Exception as e:
                logger.error(f"Failed to fetch models: {e}")
                self.console.print(f"[red]Failed to fetch models: {e}[/red]")
                return OverlayResult.ERROR, None
        
        if not available_models:
            self.console.print("[yellow]No models available for this provider[/yellow]")
            return OverlayResult.ERROR, None
        
        # Create model table
        table = Table(title="Available Models", show_header=True, header_style="bold magenta")
        table.add_column("Model", style="cyan", no_wrap=True)
        table.add_column("Description", style="white")
        table.add_column("Capabilities", style="dim")
        
        for i, model in enumerate(available_models, 1):
            # Get model info from provider config if available
            model_info = self._get_model_info(provider, model)
            
            capabilities = []
            if model_info:
                if model_info.capabilities.supports_functions:
                    capabilities.append("Functions")
                if model_info.capabilities.supports_streaming:
                    capabilities.append("Streaming")
                if model_info.capabilities.supports_vision:
                    capabilities.append("Vision")
                if model_info.capabilities.supports_code:
                    capabilities.append("Code")
            
            capabilities_text = ", ".join(capabilities) if capabilities else "Standard"
            
            # Highlight current model
            model_style = "bold cyan" if model == current_model else "cyan"
            description = model_info.description if model_info else "AI model"
            
            table.add_row(
                f"{i}. {model}",
                description,
                capabilities_text,
                style=model_style if model == current_model else None
            )
        
        self.console.print(table)
        self.console.print()
        
        # Get user selection
        try:
            choice = IntPrompt.ask(
                "Select model",
                choices=[str(i) for i in range(1, len(available_models) + 1)],
                default="1"
            )
            
            selected_model = available_models[choice - 1]
            
            self.result = OverlayResult.CONFIRMED
            self.selected_value = selected_model
            
            self.console.print(f"[green]Selected model: {selected_model}[/green]")
            return self.result, self.selected_value
            
        except KeyboardInterrupt:
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return OverlayResult.CANCELLED, None
        except Exception as e:
            logger.error(f"Model selection error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return OverlayResult.ERROR, None

    def _get_model_info(self, provider: BaseProvider, model_name: str):
        """Get model information from provider config.

        Args:
            provider: Provider instance
            model_name: Model name

        Returns:
            Model info or None
        """
        try:
            from ai_terminal.config.providers import get_provider_config, ProviderType

            # Map provider name to type
            provider_type_map = {
                "openai": ProviderType.OPENAI,
                "deepseek": ProviderType.DEEPSEEK,
                "anthropic": ProviderType.ANTHROPIC,
                "ollama": ProviderType.OLLAMA,
                "azure": ProviderType.AZURE,
            }

            provider_type = provider_type_map.get(provider.name)
            if not provider_type:
                return None

            config = get_provider_config(provider_type)

            for model_info in config.models:
                if model_info.name == model_name:
                    return model_info

            return None

        except Exception:
            return None


class ApprovalOverlay(BaseOverlay):
    """Interactive approval overlay for command execution."""

    async def show(
        self,
        title: str,
        description: str,
        command: str,
        risk_level: str = "medium"
    ) -> Tuple[OverlayResult, bool]:
        """Show approval interface.

        Args:
            title: Approval title
            description: Description of what needs approval
            command: Command to be executed
            risk_level: Risk level (low, medium, high)

        Returns:
            Tuple of (result, approved)
        """
        self.console.clear()

        # Color code by risk level
        risk_colors = {
            "low": "green",
            "medium": "yellow",
            "high": "red"
        }
        risk_color = risk_colors.get(risk_level, "yellow")

        self.show_header(f"🔒 {title}", f"Risk Level: {risk_level.upper()}")

        # Show command details
        command_panel = Panel(
            command,
            title="Command to Execute",
            border_style=risk_color,
            padding=(1, 2),
        )
        self.console.print(command_panel)

        # Show description
        if description:
            desc_text = Text(description, style="white")
            desc_panel = Panel(
                desc_text,
                title="Description",
                border_style="blue",
                padding=(1, 2),
            )
            self.console.print(desc_panel)

        # Show risk warning for high-risk commands
        if risk_level == "high":
            warning_text = Text()
            warning_text.append("⚠️  WARNING: ", style="bold red")
            warning_text.append("This command may modify your system or files. ", style="red")
            warning_text.append("Please review carefully before approving.", style="red")

            warning_panel = Panel(
                warning_text,
                border_style="red",
                padding=(1, 2),
            )
            self.console.print(warning_panel)

        self.console.print()

        # Get approval
        try:
            approved = Confirm.ask(
                f"[{risk_color}]Do you want to execute this command?[/{risk_color}]",
                default=False if risk_level == "high" else True
            )

            self.result = OverlayResult.CONFIRMED
            self.selected_value = approved

            status = "approved" if approved else "denied"
            status_color = "green" if approved else "red"
            self.console.print(f"[{status_color}]Command {status}[/{status_color}]")

            return self.result, approved

        except KeyboardInterrupt:
            self.console.print("[yellow]Approval cancelled[/yellow]")
            return OverlayResult.CANCELLED, False
        except Exception as e:
            logger.error(f"Approval error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return OverlayResult.ERROR, False


class ProgressOverlay(BaseOverlay):
    """Progress overlay for long-running operations."""

    def __init__(self, console: Optional[Console] = None):
        """Initialize progress overlay."""
        super().__init__(console)
        self.progress = None
        self.task_id = None

    def start(self, title: str, description: str = "Processing..."):
        """Start progress display.

        Args:
            title: Progress title
            description: Initial description
        """
        self.console.clear()
        self.show_header(title)

        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        )

        self.progress.start()
        self.task_id = self.progress.add_task(description, total=None)

    def update(self, description: str, advance: int = 0):
        """Update progress.

        Args:
            description: New description
            advance: Amount to advance progress
        """
        if self.progress and self.task_id is not None:
            self.progress.update(self.task_id, description=description, advance=advance)

    def stop(self, final_message: str = "Complete!"):
        """Stop progress display.

        Args:
            final_message: Final message to show
        """
        if self.progress:
            self.progress.update(self.task_id, description=final_message)
            self.progress.stop()
            self.console.print(f"[green]{final_message}[/green]")


class SessionSelectionOverlay(BaseOverlay):
    """Interactive session selection overlay."""

    async def show(self, sessions: List[Dict[str, Any]]) -> Tuple[OverlayResult, Optional[str]]:
        """Show session selection interface.

        Args:
            sessions: List of available sessions

        Returns:
            Tuple of (result, selected_session_id)
        """
        if not sessions:
            self.console.print("[yellow]No saved sessions found[/yellow]")
            return OverlayResult.ERROR, None

        self.console.clear()
        self.show_header("Select Session", "Choose a session to resume")

        # Create sessions table
        table = Table(title="Available Sessions", show_header=True, header_style="bold magenta")
        table.add_column("Session", style="cyan", no_wrap=True)
        table.add_column("Created", style="white")
        table.add_column("Messages", style="dim")
        table.add_column("Provider", style="green")
        table.add_column("Model", style="blue")

        for i, session in enumerate(sessions, 1):
            created_date = session.get("created_at", "Unknown")
            message_count = session.get("message_count", 0)
            provider = session.get("provider", "Unknown")
            model = session.get("model", "Unknown")
            session_id = session.get("session_id", "")

            # Truncate session ID for display
            display_id = session_id[:8] + "..." if len(session_id) > 8 else session_id

            table.add_row(
                f"{i}. {display_id}",
                created_date,
                str(message_count),
                provider,
                model
            )

        self.console.print(table)
        self.console.print()

        # Get user selection
        try:
            choice = IntPrompt.ask(
                "Select session",
                choices=[str(i) for i in range(1, len(sessions) + 1)],
                default="1"
            )

            selected_session = sessions[choice - 1]
            session_id = selected_session.get("session_id")

            self.result = OverlayResult.CONFIRMED
            self.selected_value = session_id

            self.console.print(f"[green]Selected session: {session_id[:8]}...[/green]")
            return self.result, session_id

        except KeyboardInterrupt:
            self.console.print("[yellow]Selection cancelled[/yellow]")
            return OverlayResult.CANCELLED, None
        except Exception as e:
            logger.error(f"Session selection error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return OverlayResult.ERROR, None


class ConfigurationOverlay(BaseOverlay):
    """Interactive configuration overlay."""

    async def show(self, current_config: Dict[str, Any]) -> Tuple[OverlayResult, Optional[Dict[str, Any]]]:
        """Show configuration interface.

        Args:
            current_config: Current configuration values

        Returns:
            Tuple of (result, updated_config)
        """
        self.console.clear()
        self.show_header("Configuration", "Modify AI Terminal settings")

        # Show current configuration
        config_table = Table(title="Current Configuration", show_header=True, header_style="bold magenta")
        config_table.add_column("Setting", style="cyan", no_wrap=True)
        config_table.add_column("Current Value", style="white")
        config_table.add_column("Description", style="dim")

        config_items = [
            ("streaming", current_config.get("streaming", True), "Enable streaming responses"),
            ("syntax_highlighting", current_config.get("syntax_highlighting", True), "Enable syntax highlighting"),
            ("compact_mode", current_config.get("compact_mode", False), "Use compact display mode"),
            ("sandbox_enabled", current_config.get("sandbox_enabled", True), "Enable command sandboxing"),
            ("auto_approve_safe", current_config.get("auto_approve_safe", False), "Auto-approve safe commands"),
        ]

        for i, (key, value, description) in enumerate(config_items, 1):
            config_table.add_row(
                f"{i}. {key}",
                str(value),
                description
            )

        self.console.print(config_table)
        self.console.print()

        # Get user selection for what to modify
        try:
            modify_choice = IntPrompt.ask(
                "Select setting to modify (0 to finish)",
                choices=[str(i) for i in range(0, len(config_items) + 1)],
                default="0"
            )

            if modify_choice == 0:
                return OverlayResult.CANCELLED, None

            # Modify selected setting
            key, current_value, description = config_items[modify_choice - 1]

            if isinstance(current_value, bool):
                new_value = Confirm.ask(
                    f"Enable {key}?",
                    default=current_value
                )
            else:
                new_value = Prompt.ask(
                    f"Enter new value for {key}",
                    default=str(current_value)
                )

            # Update configuration
            updated_config = current_config.copy()
            updated_config[key] = new_value

            self.result = OverlayResult.CONFIRMED
            self.selected_value = updated_config

            self.console.print(f"[green]Updated {key} to {new_value}[/green]")
            return self.result, updated_config

        except KeyboardInterrupt:
            self.console.print("[yellow]Configuration cancelled[/yellow]")
            return OverlayResult.CANCELLED, None
        except Exception as e:
            logger.error(f"Configuration error: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return OverlayResult.ERROR, None
