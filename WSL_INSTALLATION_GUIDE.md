# 🚀 AI Terminal - WSL Automatic Installation Guide

This guide provides a **single, fully automatic script** that installs and configures AI Terminal in your Windows 11 WSL environment.

## 📋 Prerequisites

1. **WSL2 installed** on Windows 11
2. **Ubuntu, Debian, or similar** Linux distribution in WSL
3. **Internet connection** for downloading packages
4. **AI Terminal project** exists in Windows at the specified path

## 🎯 One-Command Installation

### Step 1: Copy the Installation Script to WSL

Open your WSL terminal and run:

```bash
# Navigate to your home directory
cd ~

# Copy the installation script from Windows
cp "/mnt/c/Users/<USER>/OneDrive/Documents/New folder/auto_install_wsl.sh" ./

# Make it executable
chmod +x auto_install_wsl.sh
```

### Step 2: Run the Automatic Installation

```bash
./auto_install_wsl.sh
```

**That's it!** The script will automatically:

✅ Check WSL environment  
✅ Update system packages  
✅ Install Python 3.11+ and Poetry  
✅ Copy project files from Windows  
✅ Install all dependencies  
✅ Create convenient shortcuts  
✅ Test the installation  
✅ Launch the interactive setup wizard  
✅ Configure your AI provider and API keys  

## 🎮 What You'll Get

After installation, you'll have these convenient commands:

### 🚀 Quick Commands
- `ait` - Activate AI Terminal environment
- `ai-chat` - Start interactive chat
- `ai-quick "message"` - Send quick message
- `ai-health` - Check system health
- `ai-config` - Show configuration
- `ai-setup` - Re-run setup wizard

### 📚 Manual Commands
- `cd ~/ai-terminal` - Navigate to project
- `poetry shell` - Activate Poetry environment
- `python cli.py chat` - Start chat session

## 🔧 What the Script Does

### Phase 1: System Setup
1. **Verifies WSL environment**
2. **Updates system packages** (apt update/upgrade)
3. **Installs system dependencies** (build tools, curl, git, etc.)
4. **Installs Python 3.11+** with pip and venv
5. **Installs Poetry** package manager

### Phase 2: Project Setup
1. **Copies AI Terminal** from Windows to `~/ai-terminal`
2. **Sets proper permissions** on all files
3. **Installs Python dependencies** using Poetry or pip
4. **Creates activation scripts** and aliases

### Phase 3: Configuration
1. **Tests the installation** to ensure everything works
2. **Launches setup wizard** for interactive configuration
3. **Configures API keys** with multiple input methods
4. **Sets up shortcuts** for easy access

## 🛠️ Customization

If your project is in a different location, edit the script:

```bash
# Edit this line in auto_install_wsl.sh
WINDOWS_PROJECT_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
```

## 🐛 Troubleshooting

### Permission Issues
```bash
chmod +x auto_install_wsl.sh
chmod -R 755 ~/ai-terminal
```

### Path Issues
```bash
# Check if the Windows path exists
ls "/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
```

### Poetry Issues
```bash
# Manually add Poetry to PATH
export PATH="$HOME/.local/bin:$PATH"
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### API Key Issues
```bash
# Check environment variables
echo $DEEPSEEK_API_KEY

# Set manually if needed
echo 'export DEEPSEEK_API_KEY="your-key"' >> ~/.bashrc
source ~/.bashrc
```

## 🎯 Quick Start After Installation

1. **Restart your terminal** or run `source ~/.bashrc`
2. **Test the installation**: `ai-health`
3. **Start chatting**: `ai-chat`
4. **Send quick message**: `ai-quick "Hello AI!"`

## 📁 File Structure After Installation

```
~/ai-terminal/
├── cli.py                 # Main CLI entry point
├── ai_terminal/           # Core application code
├── pyproject.toml         # Poetry configuration
├── activate.sh            # Environment activation script
└── ...                    # Other project files
```

## 🔄 Updating

To update AI Terminal:

```bash
# Re-run the installation script
./auto_install_wsl.sh
```

The script will automatically remove the old installation and install the latest version.

## 🎉 Success!

After successful installation, you should see:

```
🎉 Installation Complete! 🎉

AI Terminal has been successfully installed and configured in WSL!

🚀 Quick Start Commands:
  ait                    - Activate AI Terminal environment
  ai-chat                - Start interactive chat
  ai-quick 'message'     - Send quick message
  ai-health              - Check system health
  ai-config              - Show configuration
  ai-setup               - Re-run setup wizard

✨ Enjoy using AI Terminal! ✨
```

Now you can start using AI Terminal in your WSL environment! 🚀
