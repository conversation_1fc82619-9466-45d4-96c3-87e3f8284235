#!/bin/bash
# Test Python detection logic for Ubuntu 24.04

echo "🐍 Testing Python Detection Logic..."
echo ""

# Check Ubuntu version
ubuntu_version=$(lsb_release -rs 2>/dev/null || echo "unknown")
echo "Ubuntu Version: $ubuntu_version"

# Check if python3 is available
if python3 --version >/dev/null 2>&1; then
    py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    major=$(echo "$py_version" | cut -d. -f1)
    minor=$(echo "$py_version" | cut -d. -f2)
    
    echo "System Python: $py_version"
    echo "Major: $major, Minor: $minor"
    
    if [[ "$major" -eq 3 && "$minor" -ge 11 ]]; then
        echo "✅ Python $py_version is compatible (3.11+)"
        echo "✅ Should use system Python instead of installing 3.11"
    else
        echo "❌ Python $py_version is not compatible (need 3.11+)"
        echo "❌ Should install Python 3.11 from deadsnakes"
    fi
else
    echo "❌ No system Python 3 found"
    echo "❌ Should install Python 3.11 from deadsnakes"
fi

echo ""

# Check if python3.11 is available
if command -v python3.11 >/dev/null 2>&1; then
    py311_version=$(python3.11 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    echo "Python 3.11: $py311_version (available)"
else
    echo "Python 3.11: Not available"
fi

echo ""

# Test the check_python_version logic
echo "Testing check_python_version logic:"

check_python_version() {
    # First try the specific Python version
    if command -v python3.11 >/dev/null 2>&1; then
        local version=$(python3.11 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        echo "$version"
        return 0
    fi
    
    # Fallback to system Python 3 (for Ubuntu 24.04 with Python 3.12)
    if command -v python3 >/dev/null 2>&1; then
        local version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        # Check if version is 3.11+ (compatible)
        local major=$(echo "$version" | cut -d. -f1)
        local minor=$(echo "$version" | cut -d. -f2)
        if [[ "$major" -eq 3 && "$minor" -ge 11 ]]; then
            echo "$version"
            return 0
        fi
    fi
    
    return 1
}

if detected_version=$(check_python_version); then
    echo "✅ Detected compatible Python: $detected_version"
    echo "✅ Installation should skip Python installation"
else
    echo "❌ No compatible Python detected"
    echo "❌ Installation should install Python 3.11"
fi

echo ""
echo "🎯 Recommendation:"
if [[ "$ubuntu_version" == "24.04" ]] && python3 --version >/dev/null 2>&1; then
    py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    echo "Use system Python $py_version (no need to install Python 3.11)"
else
    echo "Install Python 3.11 from deadsnakes PPA"
fi
